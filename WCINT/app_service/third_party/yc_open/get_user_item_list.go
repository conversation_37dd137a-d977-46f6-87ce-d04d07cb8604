package yc_open

import (
	"app_service/global"
	"app_service/pkg/util"
	"app_service/third_party/yc_open/define"
	"context"
	"encoding/json"
	"time"

	"e.coding.net/g-dtay0385/common/go-airmart-client/request/pat"
	log "e.coding.net/g-dtay0385/common/go-logger"
	utilRequest "e.coding.net/g-dtay0385/common/go-util/request"
	"e.coding.net/g-dtay0385/common/go-util/response"
)

type GetUserItemListResp struct {
	Code int32                     `json:"code" form:"code"`
	Desc string                    `json:"desc" form:"desc"`
	Data []*GetUserItemListResItem `json:"data" form:"data"`
}

type GetUserItemListResItem struct {
	ID         string `json:"_id"`
	Status     int32  `json:"status"`
	RelatedIDs struct {
		ExchangeToken   int32 `json:"exchange_token"`
		ExchangeBalance int32 `json:"exchange_balance"`
	} `json:"related_ids"`
	Extends struct {
		ReceiveType int32 `json:"receive_type"`
		QualityType int32 `json:"quality_type"`
	} `json:"extends"`
	TradeInfo struct {
		SaleMode     int32     `json:"sale_mode"`
		BuyPrice     int32     `json:"buy_price"`
		BuyTime      time.Time `json:"buy_time"`
		FusionTags   int32     `json:"fusion_tags"`
		FusionStatus int32     `json:"fusion_status"`
		StoryTags    int32     `json:"story_tags"`
		StoryStatus  int32     `json:"story_status"`
	} `json:"trade_info"`
	CreatedAt     time.Time `json:"created_at"`
	SortPrice     *int32    `json:"sort_price,omitempty"` // 使用指针处理 null
	ItemID        string    `json:"item_id"`
	ItemName      string    `json:"item_name"`
	Specification []struct {
		SpecificationNo    string `json:"specification_no"`
		SpecificationName  string `json:"specification_name"`
		SpecificationValue string `json:"specification_value"`
	} `json:"specification"`
	IconURL    string   `json:"icon_url"`
	MarketName string   `json:"market_name"`
	IPID       []string `json:"ip_id"`
	Price      struct {
		ZeroSup struct {
			SellPrice    int32 `json:"sell_price"`
			MinPrice     int32 `json:"min_price"`
			SupSellPrice int32 `json:"sup_sell_price"`
			SupMinPrice  int32 `json:"sup_min_price"`
			SellListings int32 `json:"sell_listings"`
		} `json:"zero_sup"`
		Unx struct {
			SellPrice    int32 `json:"sell_price"`
			SellListings int32 `json:"sell_listings"`
		} `json:"unx"`
	} `json:"price"`
}
type GetUserItemListReqOptions struct {
	Page               int
	PageSize           int
	Sort               string
	IgnoreReceiveTypes []any
}

type GetUserItemByItemIdListResp struct {
	Code int32                              `json:"code" form:"code"`
	Desc string                             `json:"desc" form:"desc"`
	Data []*GetUserItemsByItemIdListResItem `json:"data" form:"data"`
}

type GetUserItemsByItemIdListResItem struct {
	ID         string `json:"_id"`
	ItemID     string `json:"item_id"`
	OpenUserID string `json:"open_user_id"`
}

// 批量发货
type (
	// BatchWithdrawOrderReq 批量发货请求
	BatchWithdrawOrderReq struct {
		UserItemIDs []string `json:"user_item_ids"` // 用户商品ID列表
		// ChargeAccount string `json:"charge_account"` // 直充账号
		AddressInfo define.WithdrawOrderAddressInfo `json:"address_info"` // 收货地址
		// CouponID string `json:"coupon_id"` // 优惠券ID
		// Remark string `json:"remark"` // 备注
		OpenUserID string                   `json:"open_user_id"`             // 用户ID
		Operator   *pat.CheckAdmJwtUserInfo `json:"operator" form:"operator"` // 操作员信息
	}

	BatchWithdrawOrderResp struct {
		ID       string   `json:"_id"`
		OrderIds []string `json:"order_ids"`
	}

	GetBatchWithdrawOrderResp struct {
		Code int32                   `json:"code" form:"code"`
		Desc string                  `json:"desc" form:"desc"`
		Data *BatchWithdrawOrderResp `json:"data" form:"data"`
	}
)

// GetUserItemList 查询商品的持仓列表信息
// openUserId 可选，如果不传则查询所有用户的持仓
func GetUserItemList(ctx context.Context, openUserId string, itemIds []string, opts GetUserItemListReqOptions) ([]*GetUserItemListResItem, error) {
	rsp := &GetUserItemListResp{}

	qgBts, _ := json.Marshal([]any{[]any{"item_id", "in", itemIds}})
	queryStr := map[string]string{
		"queryGroups": string(qgBts),
	}
	params := map[string]interface{}{
		"page":        opts.Page - 1, // 云仓那边从 0 开始，本项目从 1 开始
		"limit":       opts.PageSize,
		"status_list": []any{define.UserItemStatusOwned, define.UserItemStatusForSale},
		"queryStr":    queryStr,
		"sign":        "",
	}
	// 如果传入了open_user_id，则添加到查询参数
	if openUserId != "" {
		params["open_user_id"] = openUserId
	}
	if opts.Sort != "" {
		params["sort"] = opts.Sort
	}
	if len(opts.IgnoreReceiveTypes) > 0 {
		params["ignore_receive_types"] = opts.IgnoreReceiveTypes
	}
	sign := MD5Sign(params)
	params["sign"] = sign
	log.Ctx(ctx).Infof("GetUserItemList sign:%+v", sign)
	appAccessRes, err := GetAppAccess(ctx)
	if err != nil {
		return nil, err
	}

	url := "/open/user_item/queryUserItemsByUser"
	err = utilRequest.New(ctx, utilRequest.WithUrl(global.GlobalConfig.Yc.Host+url), utilRequest.WithParams(params),
		utilRequest.WithMethodPost(),
		utilRequest.WithHeaders(utilRequest.BuildHeader("Authorization", "Bearer "+appAccessRes.AccessToken))).Call(&rsp)
	if err != nil {
		log.Ctx(ctx).Errorf("查询气仓列表，返回数据：%v", err)
		return nil, err
	}

	if rsp.Code != 0 {
		log.Ctx(ctx).Errorf("查询气仓列表，返回数据：%v", rsp)
		return nil, response.Fail.SetMsg("查询气仓列表失败")
	}
	return rsp.Data, err
}

func QueryUserItemsByUserAndItemId(ctx context.Context, openUserId string, itemId string, statusList []any, opts GetUserItemListReqOptions) ([]*GetUserItemsByItemIdListResItem, error) {
	rsp := &GetUserItemByItemIdListResp{}

	params := map[string]interface{}{
		//"page":        opts.Page - 1, // 云仓那边从 0 开始，本项目从 1 开始
		//"limit":       opts.PageSize,
		"status_list":       statusList,
		"item_id":           itemId,
		"receive_type_list": util.ToAnySlice(define.WCUserItemReceiveType),
		"sign":              "",
	}
	// 如果传入了open_user_id，则添加到查询参数
	if openUserId != "" {
		params["open_user_id"] = openUserId
	}
	if opts.Sort != "" {
		params["sort"] = opts.Sort
	}
	sign := MD5Sign(params)
	params["sign"] = sign
	log.Ctx(ctx).Infof("QueryUserItemsByUserAndItemId sign:%+v", sign)
	appAccessRes, err := GetAppAccess(ctx)
	if err != nil {
		return nil, err
	}

	url := "/open/user_item/queryUserItemsByUserAndItemId"
	err = utilRequest.New(ctx, utilRequest.WithUrl(global.GlobalConfig.Yc.Host+url), utilRequest.WithParams(params),
		utilRequest.WithMethodPost(),
		utilRequest.WithHeaders(utilRequest.BuildHeader("Authorization", "Bearer "+appAccessRes.AccessToken))).Call(&rsp)
	if err != nil {
		log.Ctx(ctx).Errorf("查询气仓列表，返回数据：%v", err)
		return nil, err
	}

	if rsp.Code != 0 {
		log.Ctx(ctx).Errorf("查询气仓列表，返回数据：%v", rsp)
		return nil, response.Fail.SetMsg("查询气仓列表失败")
	}
	return rsp.Data, err
}
