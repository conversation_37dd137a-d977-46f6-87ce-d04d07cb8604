package define

type (
	UserItemStatusEnum int32
)

const (
	UserItemStatusOwned   UserItemStatusEnum = 1   // 持有中
	UserItemStatusForSale UserItemStatusEnum = 100 // 出售中
)

// UserItemReceiveTypeEnum 背包物品获得类型
type UserItemReceiveTypeEnum int32

// WCUserItemReceiveType 文潮类型
var WCUserItemReceiveType = []UserItemReceiveTypeEnum{
	UserItemReceiveTypeFantasyMartBuy, // 二手交易
	UserItemReceiveTypeIssueItem,      // 首发商品
	UserItemReceiveTypeAirdrop,        // 空投商品
	UserItemReceiveTypeSynthesis,      // 融合
	UserItemReceiveTypeStory,          // 故事探索
}

const (
	// UserItemReceiveTypeFantasyMartBuy 二手交易
	UserItemReceiveTypeFantasyMartBuy UserItemReceiveTypeEnum = 10
	// UserItemReceiveTypeIssueItem 首发商品
	UserItemReceiveTypeIssueItem UserItemReceiveTypeEnum = 124
	// UserItemReceiveTypeAirdrop 空投商品
	UserItemReceiveTypeAirdrop UserItemReceiveTypeEnum = 125
	// UserItemReceiveTypeSynthesis 融合
	UserItemReceiveTypeSynthesis UserItemReceiveTypeEnum = 126
	// UserItemReceiveTypeStory 故事探索
	UserItemReceiveTypeStory UserItemReceiveTypeEnum = 127
	// UserItemReceiveTypeWcReSell 文潮转卖
	UserItemReceiveTypeWcReSell UserItemReceiveTypeEnum = 128
)
