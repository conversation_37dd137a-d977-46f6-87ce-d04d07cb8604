package service

import (
	"app_service/apps/business/story/service/locker"
	"app_service/apps/platform/common/constant"
	common_define "app_service/apps/platform/common/define"
	userFacade "app_service/apps/platform/user/facade"
	"app_service/global"
	"app_service/pkg/util"
	"app_service/pkg/util/kafka_util"
	"app_service/third_party/yc_open"
	"e.coding.net/g-dtay0385/common/go-util/redis_locker"
	"e.coding.net/g-dtay0385/common/go-util/response"
	"github.com/jinzhu/copier"
	"time"

	"app_service/apps/business/story/define"
	"app_service/apps/business/story/define/enums"
	"app_service/apps/business/story/repo"
	"app_service/apps/business/story/service/logic"
	"app_service/apps/platform/common/facade"
	issueFacade "app_service/apps/platform/issue/facade"
	"app_service/pkg/search"
	log "e.coding.net/g-dtay0385/common/go-logger"
)

// GetStoryRule 查询故事玩法规则
func (s *Service) GetStoryRule() (*define.GetStoryRuleResp, error) {
	resp := &define.GetStoryRuleResp{}
	err := facade.GetObj(s.ctx, enums.StoryRuleConfigKey, &resp.Content)
	if err != nil {
		log.Ctx(s.ctx).Errorf("GetStoryRuleAdmin err:%v", err)
		return nil, err
	}
	return resp, nil
}

// GetStoryWebList 查询故事玩法列表
func (s *Service) GetStoryWebList(req *define.GetStoryWebListReq) (*define.GetStoryWebListResp, error) {
	// 获取当前时间（本地时区）
	now := time.Now()
	formatNow := now.Format("2006-01-02T15:04:05.000Z07:00")

	resp := &define.GetStoryWebListResp{
		CurrentTime: formatNow,
	}
	if req.Page*req.PageSize > 100 {
		return resp, nil
	}
	isExpires := req.Status == enums.StoryStatusExpires.Val()
	storySchema := repo.GetQuery().Story
	builder := search.NewQueryBuilder().Eq(storySchema.SceneID, req.SceneId)
	statusList := []int32{enums.StoryStatusUp.Val()}
	if isExpires {
		statusList = []int32{enums.StoryStatusDown.Val(), enums.StoryStatusExpires.Val()}
	}
	builder.In(storySchema.Status, statusList)
	if !isExpires {
		builder.OrderByAsc(storySchema.StartTime)
	} else {
		builder.OrderByDesc(storySchema.EndTime)
	}
	list, count, err := repo.NewStoryRepo(storySchema.WithContext(s.ctx)).SelectPage(builder.Build(), req.GetPage(), req.GetPageSize())
	if err != nil {
		return nil, err
	}
	// 设置场景值
	scene, _ := logic.GetStoryScene(s.ctx, req.SceneId)
	if scene != nil {
		storySceneWebData := &define.GetStorySceneWebData{}
		_ = copier.Copy(storySceneWebData, scene)
		resp.StorySceneData = storySceneWebData
	}
	if len(list) == 0 {
		return resp, nil
	}
	dataList := make([]*define.GetStoryWebListData, 0)
	goodsItemIds := make([]string, 0)
	for _, v := range list {
		if util.IsValidObjectID(v.ItemID) {
			goodsItemIds = append(goodsItemIds, v.ItemID)
		}
	}
	goodsItemMap, err := issueFacade.GetIssueItemMap(s.ctx, goodsItemIds)
	if err != nil {
		return nil, err
	}
	for _, v := range list {
		data := &define.GetStoryWebListData{
			ActivityCode: v.ActivityCode,
			Title:        v.Title,
			ActivityType: v.ActivityType,
			CoverUrl:     v.CoverURL,
			TotalStock:   v.TotalStock,
			StartTime:    v.StartTime,
			EndTime:      v.EndTime,
		}
		data.Status = logic.StoryStatusHandler(v)
		if _, ok := goodsItemMap[v.ItemID]; ok {
			data.IssuerName = goodsItemMap[v.ItemID].IssuerName
			data.IssuerShortName = goodsItemMap[v.ItemID].IssuerShortName
			data.SellerName = goodsItemMap[v.ItemID].SellerName
			data.SellerShortName = goodsItemMap[v.ItemID].SellerShortName
		}
		// 设置用户参与数据
		storyJoinData, _ := logic.StoryJoinDataHandler(s.ctx, v.ID, s.GetUserId())
		data.StoryJoinData = storyJoinData
		dataList = append(dataList, data)
	}
	resp.List = dataList
	resp.Total = count
	return resp, nil
}

// GetStoryWebDetail 查询故事玩法详情
func (s *Service) GetStoryWebDetail(req *define.GetStoryWebDetailReq) (*define.GetStoryWebDetailResp, error) {
	storySchema := repo.GetQuery().Story
	builder := search.NewQueryBuilder().Eq(storySchema.ActivityCode, req.ActivityCode).In(storySchema.Status,
		[]int32{enums.StoryStatusUp.Val(), enums.StoryStatusDown.Val(), enums.StoryStatusExpires.Val()})
	getStory, err := repo.NewStoryRepo(storySchema.WithContext(s.ctx)).SelectOne(builder.Build())
	if err != nil {
		return nil, err
	}
	// 获取当前时间（本地时区）
	now := time.Now()
	formatNow := now.Format("2006-01-02T15:04:05.000Z07:00")
	resp := &define.GetStoryWebDetailResp{
		ActivityCode: getStory.ActivityCode,
		Title:        getStory.Title,
		ActivityType: getStory.ActivityType,
		CoverUrl:     getStory.CoverURL,
		ItemId:       getStory.ItemID,
		ItemTitle:    getStory.ItemTitle,
		ItemImageUrl: getStory.ItemImageURL,
		UserLimit:    getStory.UserLimit,
		TotalStock:   getStory.TotalStock,
		StartTime:    getStory.StartTime,
		EndTime:      getStory.EndTime,
		ActivityDesc: getStory.ActivityDesc,
		CurrentTime:  formatNow,
	}
	resp.Status = logic.StoryStatusHandler(getStory)
	if getStory.StockDisplay == enums.StoryStockDisplayed.Val() {
		resp.Stock = &getStory.Stock
	}
	storyMaterialsSchema := repo.GetQuery().StoryMaterials
	storyMaterialsBuilder := search.NewQueryBuilder().Eq(storyMaterialsSchema.StoryID, getStory.ID)
	storyMaterialsList, err := repo.NewStoryMaterialsRepo(storyMaterialsSchema.WithContext(s.ctx)).SelectList(storyMaterialsBuilder.Build())
	if err != nil {
		return nil, err
	}
	itemIds := make([]string, 0)
	userStoryMaterialList := make([]*define.UserStoryMaterials, 0)
	for _, material := range storyMaterialsList {
		materialsData, err := logic.UnmarshalMaterialsData(*material.MaterialsData)
		if err != nil {
			return nil, err
		}
		userStoryMaterials := &define.UserStoryMaterials{
			Id:            material.ID,
			MaterialsType: material.MaterialsType,
			Qty:           material.LimitQty,
		}
		userStoryMaterialsDatas := make([]*define.UserStoryMaterialsData, 0)
		for _, materials := range materialsData {
			itemIds = append(itemIds, materials.ItemId)
			userStoryMaterialsData := &define.UserStoryMaterialsData{
				ItemId:       materials.ItemId,
				ItemTitle:    materials.ItemTitle,
				ItemImageUrl: materials.ItemImageUrl,
				Qty:          materials.Qty,
				Loop:         materials.Loop,
				Destroy:      materials.Destroy,
			}
			userStoryMaterialsDatas = append(userStoryMaterialsDatas, userStoryMaterialsData)
		}
		userStoryMaterials.UserStoryMaterialsDatas = userStoryMaterialsDatas
		userStoryMaterialList = append(userStoryMaterialList, userStoryMaterials)
	}
	err = logic.UserStoryMaterialsHold(s.ctx, s.GetUserId(), itemIds, userStoryMaterialList)
	if err != nil {
		return nil, err
	}
	logic.CirculationStatusHold(s.ctx, itemIds, userStoryMaterialList)
	logic.UserStoryMaterialsLimitDataHold(getStory, userStoryMaterialList)
	resp.UserStoryMaterials = userStoryMaterialList
	resp.MaxLimit = logic.GetMaxLimit(s.ctx, storyMaterialsList)
	return resp, nil
}

// StoryDiscovery 发起故事玩法探索
func (s *Service) StoryDiscovery(req *define.StoryDiscoveryReq) (*define.StoryDiscoveryResp, error) {
	// 初始化锁 用户不能同时发起故事玩法
	l := redis_locker.New(global.REDIS.Client, redis_locker.WithLocker(locker.NewStoryLock(s.GetUserId(), locker.Discovery)))
	if !l.Lock(s.ctx) {
		return nil, response.TooManyRequestErr
	}
	defer l.UnLock(s.ctx)
	err := s.AddDiscoveryLock(req)
	if err != nil {
		return nil, err
	}
	storySchema := repo.GetQuery().Story
	builder := search.NewQueryBuilder().Eq(storySchema.ActivityCode, req.ActivityCode)
	getStory, err := repo.NewStoryRepo(storySchema.WithContext(s.ctx)).SelectOne(builder.Build())
	if err != nil {
		return nil, err
	}
	// 前置校验
	err = logic.VerifyLaunchStory(s.ctx, getStory, req, s.GetUserId())
	if err != nil {
		return nil, err
	}
	// 故事玩法材料
	storyMaterialsSchema := repo.GetQuery().StoryMaterials
	storyMaterialsBuilder := search.NewQueryBuilder().Eq(storyMaterialsSchema.StoryID, getStory.ID)
	storyMaterialsList, err := repo.NewStoryMaterialsRepo(storyMaterialsSchema.WithContext(s.ctx)).SelectList(storyMaterialsBuilder.Build())
	if err != nil {
		return nil, err
	}
	if req.Qty > logic.GetMaxLimit(s.ctx, storyMaterialsList) {
		return nil, common_define.CommonWarnErr
	}
	// 设置用户全选的物品id
	err = logic.SetSelectedAllUserItemIds(s.ctx, s.GetUserId(), storyMaterialsList, req)
	if err != nil {
		return nil, err
	}
	// 根据故事玩法获取选中材料的用户背包物品id
	userItemIdMap, err := logic.GetStoryMaterialUserItemIdMap(s.ctx, s.GetUserId(), storyMaterialsList, req)
	if err != nil {
		log.Ctx(s.ctx).Errorf("LaunchStory GetStoryMaterialUserItemIds err:%v", err)
		return nil, err
	}
	log.Ctx(s.ctx).Infof("LaunchStory GetStoryMaterialUserItemIds userId:%v, req:%v,userItemIds:%+v", s.GetUserId(), util.Obj2JsonStr(req), util.Obj2JsonStr(userItemIdMap))
	// 创建订单发起故事玩法
	order, err := logic.SaveStoryOrder(s.ctx, &logic.SaveStoryOrderData{UserId: s.GetUserId(), Qty: req.Qty, Story: getStory, UserItemIdMap: userItemIdMap})
	if err != nil {
		log.Ctx(s.ctx).Errorf("LaunchStory SaveStoryOrder err:%v", err)
		return nil, err
	}
	//发送Kafka
	fusion := &define.StoryFusion{StoryOrderID: order.ID}
	_ = kafka_util.SendMsg(s.ctx, constant.StoryFusion, fusion)
	resp := &define.StoryDiscoveryResp{
		ItemTitle:    getStory.ItemTitle,
		ItemId:       getStory.ItemID,
		ItemCoverUrl: getStory.CoverURL,
		Qty:          order.Qty,
		EndTime:      getStory.EndTime,
		OrderId:      order.OrderID,
	}
	return resp, nil
}

// GetStoryWebOrderList 查询故事玩法订单列表
func (s *Service) GetStoryWebOrderList(req *define.GetStoryWebOrderListReq) (*define.GetStoryWebOrderListResp, error) {
	do := repo.GetQuery().StoryOrder
	statusList := []int32{
		enums.StoryOrderStatusSuccess.Val(),
		enums.StoryOrderStatusFinish.Val(),
		enums.StoryOrderStatusDone.Val(),
	}
	builder := search.NewQueryBuilder().Eq(do.UserID, s.GetUserId()).OrderByDesc(do.ID)

	if req.SceneId != nil {
		builder.Eq(do.SceneID, *req.SceneId)
	}

	if req.Status != nil {
		switch *req.Status {
		case enums.StoryOrderStatusSuccess.Val():
			builder.Eq(do.Status, *req.Status)
		case enums.StoryOrderStatusDone.Val():
			builder.In(do.Status, []int32{
				enums.StoryOrderStatusFinish.Val(),
				enums.StoryOrderStatusDone.Val(),
			})
		default:
			builder.In(do.Status, statusList)
		}
	} else {
		builder.In(do.Status, statusList)
	}

	list, count, err := repo.NewStoryOrderRepo(do.WithContext(s.ctx)).SelectPage(
		builder.Build(),
		req.GetPage(),
		req.GetPageSize(),
	)
	if err != nil {
		log.Ctx(s.ctx).Errorf("GetStoryWebOrderList err:%v", err)
		return nil, err
	}
	resp := &define.GetStoryWebOrderListResp{
		List:  make([]*define.GetStoryOrderListData, 0),
		Total: count,
	}
	if len(list) == 0 {
		return resp, nil
	}
	sids := make([]int64, 6)
	for _, v := range list {
		sids = append(sids, v.StoryID)
	}
	activityList, err := logic.GetStoryMap(s.ctx, sids)
	if err != nil {
		log.Ctx(s.ctx).Errorf("GetStoryWebOrderList err:%v", err)
		return nil, err
	}
	for _, v := range list {
		aType := int32(0)
		if activityList[v.StoryID] != nil {
			aType = activityList[v.StoryID].ActivityType
		}
		storyOrderListData := &define.GetStoryOrderListData{
			ActivityType: aType,
			CreatedAt:    v.CreatedAt,
			ItemCoverURL: v.ItemCoverURL,
			ItemTitle:    v.ItemTitle,
			OrderID:      v.OrderID,
			Qty:          v.Qty,
			Status:       v.Status,
		}
		scene, _ := logic.GetStoryScene(s.ctx, v.SceneID)
		storyOrderListData.SceneName = scene.Name
		if activityList[v.StoryID] != nil {
			storyOrderListData.EndTime = activityList[v.StoryID].EndTime
			storyOrderListData.Title = activityList[v.StoryID].Title
			storyOrderListData.CoverUrl = activityList[v.StoryID].CoverURL
		}
		resp.List = append(resp.List, storyOrderListData)
	}
	return resp, nil
}

// GetStoryWebOrderDetail 查询故事玩法订单详情
func (s *Service) GetStoryWebOrderDetail(req *define.GetStoryWebOrderDetailReq) (*define.GetStoryWebOrderDetailResp, error) {
	storyOrderSchema := repo.GetQuery().StoryOrder
	builder := search.NewQueryBuilder().
		Eq(storyOrderSchema.OrderID, req.OrderID).
		Eq(storyOrderSchema.UserID, s.GetUserId())
	order, err := repo.NewStoryOrderRepo(storyOrderSchema.WithContext(s.ctx)).SelectOne(builder.Build())
	if err != nil {
		log.Ctx(s.ctx).Errorf("GetStoryAdminOrderDetail err:%v", err)
		return nil, err
	}

	om, err := logic.GetStoryOrderDetailUQ(s.ctx, order.ID)
	if err != nil {
		log.Ctx(s.ctx).Errorf("GetStoryAdminOrderDetail err:%v", err)
		return nil, err
	}
	err = repo.GetDB().Model(order).Association("Story").Find(&order.Story)
	if err != nil {
		log.Ctx(s.ctx).Errorf("GetStoryAdminOrderDetail err:%v", err)
		return nil, err
	}
	materials := make([]*define.GetStoryOrderDetailMaterials, 0)
	for _, item := range om {
		materials = append(materials, &define.GetStoryOrderDetailMaterials{
			ItemName: item.MaterialsItemName,
			ItemUrl:  item.MaterialsItemURL,
			Qty:      item.Qty,
		})
	}
	storySchema := repo.GetQuery().Story
	story, err := repo.NewStoryRepo(storySchema.WithContext(s.ctx)).SelectOne(search.NewQueryBuilder().Eq(storySchema.ID, order.StoryID).Build())
	resp := &define.GetStoryWebOrderDetailResp{
		ActivityType: order.Story.ActivityType,
		ChainHash:    order.ChainHash,
		CreatedAt:    order.CreatedAt,
		ItemCoverURL: order.ItemCoverURL,
		ItemTitle:    order.ItemTitle,
		Materials:    materials,
		OrderID:      order.OrderID,
		Qty:          order.Qty,
		Status:       order.Status,
		EndTime:      story.EndTime,
		CoverUrl:     story.CoverURL,
	}

	return resp, nil
}

// GetStoryWebOrderStatus 获取故事玩法订单状态
func (s *Service) GetStoryWebOrderStatus(req *define.GetStoryWebOrderStatusReq) (*define.GetStoryWebOrderStatusResp, error) {
	storyOrderSchema := repo.GetQuery().StoryOrder
	builder := search.NewQueryBuilder().
		Eq(storyOrderSchema.OrderID, req.OrderID).
		Eq(storyOrderSchema.UserID, s.GetUserId())
	order, err := repo.NewStoryOrderRepo(storyOrderSchema.WithContext(s.ctx)).SelectOne(builder.Build())
	if err != nil {
		log.Ctx(s.ctx).Errorf("GetStoryWebOrderStatus err:%v", err)
		return nil, err
	}
	resp := &define.GetStoryWebOrderStatusResp{
		ItemTitle:    order.ItemTitle,
		ItemId:       order.ItemID,
		ItemCoverUrl: order.ItemCoverURL,
		Qty:          order.Qty,
		Status:       order.Status,
	}
	return resp, nil
}

// GetStoryUserMaterialsList 获取我的故事玩法探索材料列表
func (s *Service) GetStoryUserMaterialsList(req *define.GetStoryUserMaterialsListReq) (*define.GetStoryUserMaterialsListResp, error) {
	openUserId, err := userFacade.GetOpenUserId(s.ctx, s.GetUserId())
	if err != nil || openUserId == "" {
		return nil, common_define.CommonErr
	}
	itemInfo, err := issueFacade.GetIssueItemByItemID(s.ctx, req.ItemId)
	if err != nil {
		log.Ctx(s.ctx).Errorf("GetStoryUserMaterialsList 获取 issue_item 失败: %v, item_id: %s", err, req.ItemId)
		return nil, common_define.CommonErr
	}
	queryUserItemsRes, err := yc_open.QueryUserItems(s.ctx, openUserId, req.ItemId, &req.CostPriceOrder, req.Page, req.PageSize)
	if err != nil {
		log.Ctx(s.ctx).Errorf("GetStoryUserMaterialsList 从云仓获取 user_item_list 失败: %v", err)
		return nil, err
	}
	resp := &define.GetStoryUserMaterialsListResp{}
	if queryUserItemsRes == nil {
		return resp, nil
	}
	dataList := make([]*define.GetStoryUserMaterialsListData, 0)
	userItems := queryUserItemsRes.List
	for _, v := range userItems {
		dataList = append(dataList, &define.GetStoryUserMaterialsListData{
			UserItemId:   v.ID,
			ItemId:       v.ItemID,
			ItemTitle:    itemInfo.ItemName,
			ItemCoverUrl: itemInfo.ImageURL,
			CostPrice:    v.CostPrice,
			FusionTags:   v.FusionTags,
			StoryTags:    v.StoryTags,
		})
	}
	resp.List = dataList
	resp.Total = queryUserItemsRes.Total
	return resp, nil
}

func (s *Service) AddDiscoveryLock(req *define.StoryDiscoveryReq) error {
	userItemIds := make([]string, 0)
	for _, v := range req.StoryMaterials {
		for _, info := range v.MaterialsItemInfo {
			userItemIds = append(userItemIds, info.UserItemIds...)
		}
	}
	if len(userItemIds) == 0 {
		return nil
	}
	l := redis_locker.New(global.REDIS.Client, redis_locker.WithLocker(locker.NewStoryLock(util.StrVal(userItemIds), locker.DiscoveryUserItemId)))
	if !l.Lock(s.ctx) {
		return response.TooManyRequestErr
	}
	return nil
}
