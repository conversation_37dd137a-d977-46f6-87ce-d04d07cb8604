package service

import (
	"app_service/apps/platform/common/constant"
	"app_service/third_party/yc_open"
	"context"
	"encoding/json"
	"time"

	issueModel "app_service/apps/platform/issue/dal/model/mongdb"
	userFacade "app_service/apps/platform/user/facade"
	"github.com/jinzhu/copier"

	"app_service/apps/business/synthesis/dal/model"
	"app_service/apps/business/synthesis/define"
	"app_service/apps/business/synthesis/define/enums"
	"app_service/apps/business/synthesis/repo"
	"app_service/apps/business/synthesis/service/logic"
	"app_service/apps/platform/common/facade"
	"app_service/apps/platform/issue/dal/model/mongdb"
	issueDefine "app_service/apps/platform/issue/define"
	issueFacade "app_service/apps/platform/issue/facade"
	"app_service/pkg/search"
	"app_service/pkg/util"
	"app_service/pkg/util/excelize_lib"
	"app_service/pkg/util/snowflakeutl"
	log "e.coding.net/g-dtay0385/common/go-logger"
	"e.coding.net/g-dtay0385/common/go-util/response"
	"github.com/gin-gonic/gin"
	"gorm.io/datatypes"
)

// GetSynthesisList 查询合成管理列表
func (s *Service) GetSynthesisList(req *define.GetSynthesisAdminListReq) (*define.GetSynthesisAdminListResp, error) {
	synthesisSchema := repo.GetQuery().Synthesis
	builder := search.NewQueryBuilder().OrderByDesc(synthesisSchema.ID)
	if !req.StartTimeStart.IsZero() && !req.StartTimeEnd.IsZero() {
		builder = builder.Gte(synthesisSchema.StartTime, req.StartTimeStart)
		builder = builder.Lte(synthesisSchema.StartTime, req.StartTimeEnd)
	}
	if !req.CreatedAtStart.IsZero() && !req.CreatedAtEnd.IsZero() {
		builder = builder.Gte(synthesisSchema.CreatedAt, req.CreatedAtStart)
		builder = builder.Lte(synthesisSchema.CreatedAt, req.CreatedAtEnd)
	}
	if req.ID != nil {
		builder = builder.Eq(synthesisSchema.ID, *req.ID)
	}
	if req.Title != "" {
		builder = builder.Like(synthesisSchema.Title, "%"+req.Title+"%")
	}
	if req.ItemId != "" {
		builder = builder.Eq(synthesisSchema.ItemID, req.ItemId)
	}
	if req.ItemTitle != "" {
		builder = builder.Like(synthesisSchema.ItemTitle, "%"+req.ItemTitle+"%")
	}
	if req.Status != nil {
		builder = builder.Eq(synthesisSchema.Status, *req.Status)
	}
	if req.ActivityType != nil {
		builder = builder.Eq(synthesisSchema.ActivityType, *req.ActivityType)
	}
	if req.CreatedBy != "" {
		builder = builder.Eq(synthesisSchema.CreatedBy, req.CreatedBy)
	}
	list, count, err := repo.NewSynthesisRepo(synthesisSchema.WithContext(s.ctx)).SelectPage(builder.Build(), req.GetPage(), req.GetPageSize())
	if err != nil {
		return nil, err
	}
	resp := &define.GetSynthesisAdminListResp{}
	if len(list) == 0 {
		return resp, nil
	}
	dataList := make([]*define.GetSynthesisAdminListData, 0)
	for _, v := range list {
		dataList = append(dataList, &define.GetSynthesisAdminListData{
			ID:              v.ID,
			ActivityCode:    v.ActivityCode,
			Title:           v.Title,
			ActivityType:    v.ActivityType,
			CoverUrl:        v.CoverURL,
			Status:          v.Status,
			ItemId:          v.ItemID,
			ItemTitle:       v.ItemTitle,
			UserLimit:       v.UserLimit,
			CompleteUserNum: v.CompleteUserNum,
			Stock:           v.Stock,
			TotalStock:      v.TotalStock,
			StartTime:       v.StartTime,
			EndTime:         v.EndTime,
			CreatedAt:       v.CreatedAt,
			CreatedBy:       v.CreatedBy,
			UpdatedAt:       v.UpdatedAt,
			UpdatedBy:       v.UpdatedBy,
		})
	}
	resp.List = dataList
	resp.Total = count
	return resp, nil
}

// GetSynthesisDetail 查询合成管理详情
func (s *Service) GetSynthesisDetail(req *define.GetSynthesisAdminDetailReq) (*define.GetSynthesisAdminDetailResp, error) {
	synthesisSchema := repo.GetQuery().Synthesis
	builder := search.NewQueryBuilder().Eq(synthesisSchema.ID, req.ID)
	synthesis, err := repo.NewSynthesisRepo(synthesisSchema.WithContext(s.ctx)).SelectOne(builder.Build())
	if err != nil {
		return nil, err
	}
	resp := &define.GetSynthesisAdminDetailResp{
		ID:              synthesis.ID,
		ActivityCode:    synthesis.ActivityCode,
		Title:           synthesis.Title,
		ActivityType:    synthesis.ActivityType,
		CoverUrl:        synthesis.CoverURL,
		Status:          synthesis.Status,
		ItemId:          synthesis.ItemID,
		ItemTitle:       synthesis.ItemTitle,
		UserLimit:       synthesis.UserLimit,
		CompleteUserNum: synthesis.CompleteUserNum,
		Stock:           synthesis.Stock,
		TotalStock:      synthesis.TotalStock,
		StockDisplay:    synthesis.StockDisplay,
		StartTime:       synthesis.StartTime,
		EndTime:         synthesis.EndTime,
		ActivityDesc:    synthesis.ActivityDesc,
		CreatedAt:       synthesis.CreatedAt,
		CreatedBy:       synthesis.CreatedBy,
		UpdatedAt:       synthesis.UpdatedAt,
		UpdatedBy:       synthesis.UpdatedBy,
	}
	synthesisMaterialsSchema := repo.GetQuery().SynthesisMaterials
	synthesisMaterialsBuilder := search.NewQueryBuilder().Eq(synthesisMaterialsSchema.SynthesisID, synthesis.ID)
	synthesisMaterialsList, err := repo.NewSynthesisMaterialsRepo(synthesisMaterialsSchema.WithContext(s.ctx)).SelectList(synthesisMaterialsBuilder.Build())
	if err != nil {
		return nil, err
	}
	for _, material := range synthesisMaterialsList {
		materialsData, err := logic.UnmarshalMaterialsData(*material.MaterialsData)
		if err != nil {
			return nil, err
		}

		resp.SynthesisMaterialsData = append(resp.SynthesisMaterialsData, &define.SynthesisMaterialsDetailData{
			SynthesisMaterialsId:     material.ID,
			MaterialsType:            material.MaterialsType,
			Qty:                      material.LimitQty,
			SynthesisMaterialsDetail: materialsData,
		})
	}
	if synthesis.ActivityType == enums.Rights.Val() {
		priorityBuy, err := issueFacade.GetPriorityBuy(s.ctx, synthesis.ItemID)
		if err != nil {
			return nil, err
		}
		issueItem, err := issueFacade.GetIssueItemByItemID(s.ctx, priorityBuy.ItemID.Hex())
		if err != nil {
			return nil, err
		}
		resp.PriorityBuyData = &define.SynthesisPrizeData{
			ItemImageUrl: issueItem.ImageURL,
			ItemName:     issueItem.ItemName,
			ItemPrice:    issueItem.Price,
		}
		stock, err := logic.GetSynthesisTotalStock(s.ctx, synthesis.ItemID)
		if err != nil {
			return nil, err
		}
		if issueItem.PriorityBuy.Stock != nil {
			resp.PriorityBuyData.UsableStock = *issueItem.PriorityBuy.Stock - stock
		}
	} else {
		issueItem, err := issueFacade.GetIssueItemByItemID(s.ctx, synthesis.ItemID)
		if err != nil {
			return nil, err
		}
		resp.ItemData = &define.SynthesisPrizeData{
			ItemImageUrl: issueItem.ImageURL,
			ItemName:     issueItem.ItemName,
			ItemPrice:    issueItem.Price,
		}
		stock, err := logic.GetSynthesisTotalStock(s.ctx, synthesis.ItemID)
		if err != nil {
			return nil, err
		}
		if issueItem.Synthesis.Stock != nil {
			resp.ItemData.UsableStock = *issueItem.Synthesis.Stock - stock
		}
	}
	releaseTimeData, err := logic.GetSynthesisReleaseTime(synthesis.ReleaseTime)
	if err != nil {
		return nil, err
	}
	resp.Release = releaseTimeData
	return resp, nil
}

// AddSynthesis 新增合成活动
func (s *Service) AddSynthesis(req *define.AddSynthesisReq) (*define.AddSynthesisResp, error) {
	synthesisSchema := repo.GetQuery().Synthesis
	getSynthesis, _ := repo.NewSynthesisRepo(synthesisSchema.WithContext(s.ctx)).SelectOne(search.NewQueryBuilder().Eq(synthesisSchema.Title, req.Title).Build())
	if getSynthesis != nil {
		return nil, define.SH100006Err
	}
	resp := &define.AddSynthesisResp{}

	synthesis := &model.Synthesis{
		ActivityCode: util.StrVal(snowflakeutl.GenerateID()),
		Title:        req.Title,
		ActivityType: req.ActivityType,
		CoverURL:     req.CoverUrl,
		ItemID:       req.ItemId,
		ItemTitle:    req.ItemTitle,
		Stock:        req.TotalStock,
		TotalStock:   req.TotalStock,
		StockDisplay: req.StockDisplay,
		UserLimit:    *req.UserLimit,
		ActivityDesc: req.ActivityDesc,
		StartTime:    req.StartTime,
		EndTime:      req.EndTime,
		CreatedBy:    s.GetAdminId(),
	}
	url, err := logic.GetSynthesisItemImageUrl(s.ctx, req.ActivityType, req.ItemId)
	if err != nil {
		return nil, err
	}
	synthesis.ItemImageURL = url
	// materialsType 1核心材料 2 crux材料
	// 关键材料组数
	cruxMaterialGroupLength := 0
	// 核心材料组数
	coreMaterialGroupLength := 0
	// 核心材料最大组数
	coreMaterialMaxGroupLength := 1
	// 关键材料最大组数
	cruxMaterialMaxGroupLength := 10
	// 关键一组材料最大集齐数量
	cruxMaterialLimitMaxQty := int32(10)
	// 一组材料最大商品数量
	materialLimitMaxQty := 20

	for _, item := range req.AddSynthesisMaterials {
		if item.MaterialsType == enums.SynthesisMaterialsTypeCore.Val() {
			item.Qty = int32(len(item.AddSynthesisMaterials))
			coreMaterialGroupLength++
		} else {
			if int32(len(item.AddSynthesisMaterials)) <= item.Qty {
				return nil, define.SH100004Err
			}
			cruxMaterialGroupLength++
			if item.Qty > cruxMaterialLimitMaxQty {
				return nil, define.SH100003Err
			}
		}
		if len(item.AddSynthesisMaterials) > materialLimitMaxQty {
			return nil, define.SH100002Err
		}
	}
	if coreMaterialGroupLength > coreMaterialMaxGroupLength {
		return nil, define.SH100001Err
	}
	if cruxMaterialGroupLength > cruxMaterialMaxGroupLength {
		return nil, define.SH100005Err
	}
	var usableStock int32
	if req.ActivityType == enums.Rights.Val() {
		usableStock, err = logic.GetPriorityBuyUsableStock(s.ctx, synthesis.ItemID)
		if err != nil {
			return nil, err
		}
		if usableStock < req.TotalStock {
			return nil, define.SH100009Err
		}
	} else {
		usableStock, err = logic.GetItemUsableStock(s.ctx, synthesis.ItemID)
		if err != nil {
			return nil, err
		}
		if usableStock < req.TotalStock {
			return nil, define.SH1000019Err
		}
	}
	releaseFlag := false
OuterLoop:
	for _, material := range req.AddSynthesisMaterials {
		for _, item := range material.AddSynthesisMaterials {
			if item.Loop == constant.Yes {
				releaseFlag = true
				break OuterLoop
			}
		}
	}
	if releaseFlag {
		if req.Release == nil {
			return nil, define.SH1000016Err
		} else {
			if req.Release.ReleaseTime != nil && !req.Release.ReleaseTime.IsZero() {
				req.Release.ReleaseTime = util.GetBeijingStartOfDay(req.Release.ReleaseTime)
				if req.Release.ReleaseTime.Before(time.Now()) {
					return nil, define.SH1000017Err
				}
			}
			if req.Release.ReleaseDay != nil && *req.Release.ReleaseDay < 0 {
				return nil, define.SH1000017Err
			}
			var jsonData datatypes.JSON
			jsonData = datatypes.JSON(util.Obj2JsonStr(req.Release))
			synthesis.ReleaseTime = &jsonData
		}
	}
	err = repo.ExecGenTx(s.ctx, func(tx context.Context) error {
		err := repo.NewSynthesisRepo(repo.Query(tx).Synthesis.WithContext(tx)).Save(synthesis)
		if err != nil {
			return err
		}
		synthesisMaterialsList := make([]*model.SynthesisMaterials, 0)
		for _, item := range req.AddSynthesisMaterials {
			var jsonData datatypes.JSON
			jsonData = datatypes.JSON(util.Obj2JsonStr(item.AddSynthesisMaterials))
			synthesisMaterials := &model.SynthesisMaterials{
				SynthesisID:   synthesis.ID,
				MaterialsType: item.MaterialsType,
				LimitQty:      item.Qty,
				MaterialsData: &jsonData,
				CreatedBy:     s.GetAdminId(),
			}
			synthesisMaterialsList = append(synthesisMaterialsList, synthesisMaterials)
		}
		err = repo.NewSynthesisMaterialsRepo(repo.Query(tx).SynthesisMaterials.WithContext(tx)).BatchSave(synthesisMaterialsList, 100)
		if err != nil {
			return err
		}
		return nil
	})
	if err != nil {
		return nil, err
	}

	// 日志
	_ = logic.NewSynthesisLog(s.ctx, enums.LogActionCreate, synthesis.ID).Save(s.GetAdminId())
	resp.ID = synthesis.ID
	return resp, nil
}

// EditSynthesis 编辑合成活动
func (s *Service) EditSynthesis(req *define.EditSynthesisReq) (*define.EditSynthesisResp, error) {
	resp := &define.EditSynthesisResp{}
	synthesisSchema := repo.GetQuery().Synthesis
	getSynthesis, err := repo.NewSynthesisRepo(synthesisSchema.WithContext(s.ctx)).SelectOne(search.NewQueryBuilder().Eq(synthesisSchema.ID, req.ID).Build())
	if err != nil {
		return nil, err
	}
	if getSynthesis != nil && getSynthesis.Title != req.Title {
		count, err := repo.NewSynthesisRepo(synthesisSchema.WithContext(s.ctx)).Count(search.NewQueryBuilder().Eq(synthesisSchema.Title, req.Title).
			Ne(synthesisSchema.ID, req.ID).Build())
		if err != nil {
			return nil, err
		}
		if count > 0 {
			return nil, define.SH100006Err
		}
	}

	// 初始化int数组
	statusList := []int32{enums.SynthesisStatusDown.Val(), enums.SynthesisStatusExpires.Val(), enums.SynthesisStatusDel.Val()}
	synthesisLog := logic.NewSynthesisLog(s.ctx, enums.LogActionUpdate, req.ID)
	if util.FindIntInSlice(getSynthesis.Status, statusList) {
		return nil, define.SH100007Err
	} else if getSynthesis.Status == enums.SynthesisStatusUp.Val() {
		// 仅可修改-合成时间，合成说明，封面图
		updateSynthesis := &model.Synthesis{
			ID:           getSynthesis.ID,
			StartTime:    req.StartTime,
			EndTime:      req.EndTime,
			ActivityDesc: req.ActivityDesc,
			CoverURL:     req.CoverUrl,
			UpdatedBy:    s.GetAdminId(),
		}
		err := repo.NewSynthesisRepo(synthesisSchema.WithContext(s.ctx)).UpdateById(updateSynthesis)
		if err != nil {
			return resp, err
		}
		synthesisLog.SetMain(getSynthesis, updateSynthesis)
	} else if getSynthesis.Status == enums.SynthesisStatusWaiting.Val() {
		// 所有内容都可以修改
		synthesisMaterialsSchema := repo.GetQuery().SynthesisMaterials
		synthesisMaterialsBuilder := search.NewQueryBuilder().Eq(synthesisMaterialsSchema.SynthesisID, req.ID)
		synthesisMaterialsList, err := repo.NewSynthesisMaterialsRepo(synthesisMaterialsSchema.WithContext(s.ctx)).SelectList(synthesisMaterialsBuilder.Build())
		if err != nil {
			return nil, err
		}
		// 活动信息修改
		updateSynthesis := &model.Synthesis{
			ID:           getSynthesis.ID,
			Title:        req.Title,
			ActivityType: req.ActivityType,
			CoverURL:     req.CoverUrl,
			ItemID:       req.ItemId,
			ItemTitle:    req.ItemTitle,
			Stock:        req.TotalStock,
			TotalStock:   req.TotalStock,
			StockDisplay: req.StockDisplay,
			UserLimit:    *req.UserLimit,
			ActivityDesc: req.ActivityDesc,
			StartTime:    req.StartTime,
			EndTime:      req.EndTime,
			UpdatedBy:    s.GetAdminId(),
		}
		url, err := logic.GetSynthesisItemImageUrl(s.ctx, req.ActivityType, req.ItemId)
		if err != nil {
			return nil, err
		}
		updateSynthesis.ItemImageURL = url

		// materialsType 1核心材料 2 crux材料
		// 关键材料组数
		cruxMaterialGroupLength := 0
		// 核心材料组数
		coreMaterialGroupLength := 0
		// 核心材料最大组数
		coreMaterialMaxGroupLength := 1
		// 关键材料最大组数
		cruxMaterialMaxGroupLength := 10
		// 关键一组材料最大集齐数量
		cruxMaterialLimitMaxQty := int32(10)
		// 一组材料最大商品数量
		materialLimitMaxQty := 20

		addSynthesisMaterials := make([]*model.SynthesisMaterials, 0)
		delSynthesisMaterials := make([]*model.SynthesisMaterials, 0)
		for _, item := range req.EditSynthesisMaterials {
			if item.MaterialsType == enums.SynthesisMaterialsTypeCore.Val() {
				item.Qty = int32(len(item.EditSynthesisMaterials))
				coreMaterialGroupLength++
			} else {
				if int32(len(item.EditSynthesisMaterials)) <= item.Qty {
					return nil, define.SH100004Err
				}
				cruxMaterialGroupLength++
				if item.Qty > cruxMaterialLimitMaxQty {
					return nil, define.SH100003Err
				}
			}
			if len(item.EditSynthesisMaterials) > materialLimitMaxQty {
				return nil, define.SH100002Err
			}
			if coreMaterialGroupLength > coreMaterialMaxGroupLength {
				return nil, define.SH100001Err
			}
			if cruxMaterialGroupLength > cruxMaterialMaxGroupLength {
				return nil, define.SH100005Err
			}

			var jsonData datatypes.JSON
			jsonData = datatypes.JSON(util.Obj2JsonStr(item.EditSynthesisMaterials))
			addSynthesisMaterials = append(addSynthesisMaterials, &model.SynthesisMaterials{
				SynthesisID:   getSynthesis.ID,
				MaterialsType: item.MaterialsType,
				LimitQty:      item.Qty,
				MaterialsData: &jsonData,
				CreatedBy:     s.GetAdminId(),
			})
		}
		for _, item := range synthesisMaterialsList {
			delSynthesisMaterials = append(delSynthesisMaterials, &model.SynthesisMaterials{
				ID: item.ID,
			})
		}
		log.Ctx(s.ctx).Debugf("编辑活动材料 addSynthesisMaterials:%+v", util.Obj2JsonStr(addSynthesisMaterials))
		log.Ctx(s.ctx).Debugf("编辑活动材料 delSynthesisMaterials:%+v", util.Obj2JsonStr(delSynthesisMaterials))
		var usableStock int32
		if req.ActivityType == enums.Rights.Val() {
			usableStock, err = logic.GetPriorityBuyUsableStock(s.ctx, req.ItemId)
			if err != nil {
				return nil, err
			}
			if usableStock < req.TotalStock {
				return nil, define.SH100009Err
			}
		} else {
			usableStock, err = logic.GetItemUsableStock(s.ctx, req.ItemId)
			if err != nil {
				return nil, err
			}
			if usableStock < req.TotalStock {
				return nil, define.SH1000019Err
			}
		}
		releaseFlag := false
	OuterLoop:
		for _, material := range req.EditSynthesisMaterials {
			for _, item := range material.EditSynthesisMaterials {
				if item.Loop == constant.Yes {
					releaseFlag = true
					break OuterLoop
				}
			}
		}
		if releaseFlag {
			if req.Release == nil {
				return nil, define.SH1000016Err
			} else {
				var jsonData datatypes.JSON
				if req.Release.ReleaseTime != nil && !req.Release.ReleaseTime.IsZero() {
					req.Release.ReleaseTime = util.GetBeijingStartOfDay(req.Release.ReleaseTime)
					if req.Release.ReleaseTime.Before(time.Now()) {
						return nil, define.SH1000017Err
					}
				}
				if req.Release.ReleaseDay != nil && *req.Release.ReleaseDay < 0 {
					return nil, define.SH1000017Err
				}
				jsonData = datatypes.JSON(util.Obj2JsonStr(req.Release))
				updateSynthesis.ReleaseTime = &jsonData
			}
		}
		// db操作
		err = repo.ExecGenTx(s.ctx, func(tx context.Context) error {
			err = repo.NewSynthesisRepo(repo.Query(tx).Synthesis.WithContext(tx)).UpdateById(updateSynthesis)
			if err != nil {
				return err
			}
			err = repo.NewSynthesisMaterialsRepo(repo.Query(tx).SynthesisMaterials.WithContext(tx)).BatchSave(addSynthesisMaterials, 100)
			if err != nil {
				log.Ctx(s.ctx).Errorf("编辑活动失败,新增活动材料失败 err:%v", err)
				return err
			}
			err = repo.NewSynthesisMaterialsRepo(repo.Query(tx).SynthesisMaterials.WithContext(tx)).RemoveByIds(delSynthesisMaterials...)
			if err != nil {
				log.Ctx(s.ctx).Errorf("编辑活动失败,删除活动材料失败 err:%v", err)
				return err
			}
			return nil
		})
		if err != nil {
			log.Ctx(s.ctx).Errorf("编辑活动失败 err:%v", err)
			return nil, err
		}
		synthesisLog.SetMaterials(synthesisMaterialsList, addSynthesisMaterials)
	}
	// 保存日志
	_ = synthesisLog.Save(s.GetAdminId())

	resp.ID = req.ID
	return resp, nil
}

// EditSynthesisStatus 合成活动状态编辑
func (s *Service) EditSynthesisStatus(req *define.EditSynthesisStatusReq) (*define.EditSynthesisStatusResp, error) {
	// 加载东八区时区
	loc, _ := time.LoadLocation("Asia/Shanghai")
	logAction := enums.LogActionUp
	synthesisSchema := repo.GetQuery().Synthesis
	builder := search.NewQueryBuilder().Eq(synthesisSchema.ID, req.ID)
	m := &model.Synthesis{
		Status: req.Status,
	}
	if req.Status == enums.SynthesisStatusUp.Val() {
		builder = builder.Eq(synthesisSchema.Status, enums.SynthesisStatusWaiting.Val())
		if req.Status == enums.SynthesisStatusUp.Val() {
			getSynthesis, err := repo.NewSynthesisRepo(synthesisSchema.WithContext(s.ctx)).SelectOne(search.NewQueryBuilder().Eq(synthesisSchema.ID, req.ID).Build())
			if err != nil {
				return nil, err
			}
			if getSynthesis.ActivityType == enums.Rights.Val() {
				// 如果是优先购查询可用库存
				priorityBuy, err := issueFacade.GetPriorityBuy(s.ctx, getSynthesis.ItemID)
				if err != nil {
					return nil, err
				}
				if priorityBuy.Status != issueModel.PriorityBuyStatusUp {
					return nil, define.SH100008Err
				}
				issueItem, err := issueFacade.GetIssueItemByItemID(s.ctx, priorityBuy.ItemID.Hex())
				if err != nil {
					return nil, err
				}
				if issueItem.IssueTime != nil && getSynthesis.EndTime.After(issueItem.IssueTime.In(loc)) {
					return nil, define.SH1000021Err
				}
				// 查询合成活动已用库存
				synthesisTotalStock, err := logic.GetSynthesisTotalStock(s.ctx, getSynthesis.ItemID)
				if err != nil {
					return nil, err
				}
				if issueItem.PriorityBuy.Stock != nil {
					if *issueItem.PriorityBuy.Stock-synthesisTotalStock-getSynthesis.TotalStock < 0 {
						return nil, define.SH100009Err
					}
				} else {
					return nil, define.SH100009Err
				}
			} else {
				issueItem, err := issueFacade.GetIssueItemByItemID(s.ctx, getSynthesis.ItemID)
				if err != nil {
					return nil, err
				}
				if issueItem.AuditStatus != mongdb.IssueItemAuditStatusApproved {
					return nil, define.SH1000018Err
				}
				if issueItem.Synthesis.Status != mongdb.IssueItemSynthesisStatusOpen {
					return nil, define.SH1000022Err
				}

				if issueItem.IssueTime != nil && getSynthesis.StartTime.Before(issueItem.IssueTime.In(loc)) {
					return nil, define.SH1000020Err
				}
				// 查询合成活动已用库存
				synthesisTotalStock, err := logic.GetSynthesisTotalStock(s.ctx, getSynthesis.ItemID)
				if err != nil {
					return nil, err
				}
				if issueItem.Synthesis.Stock != nil {
					if *issueItem.Synthesis.Stock-synthesisTotalStock-getSynthesis.TotalStock < 0 {
						return nil, define.SH1000019Err
					}
				} else {
					return nil, define.SH1000019Err
				}
			}
		}
	} else if req.Status == enums.SynthesisStatusDown.Val() {
		builder = builder.Eq(synthesisSchema.Status, enums.SynthesisStatusUp.Val())
		logAction = enums.LogActionDown
	}
	err := repo.NewSynthesisRepo(synthesisSchema.WithContext(s.ctx)).Update(m, builder.Build())
	if err != nil {
		return nil, err
	}
	// 日志
	_ = logic.NewSynthesisLog(s.ctx, logAction, req.ID).Save(s.GetAdminId())
	return &define.EditSynthesisStatusResp{}, nil
}

// DelSynthesis 合成活动删除
func (s *Service) DelSynthesis(req *define.DelSynthesisReq) (*define.DelSynthesisResp, error) {
	synthesisSchema := repo.GetQuery().Synthesis
	m := &model.Synthesis{
		Status: enums.SynthesisStatusDel.Val(),
	}
	err := repo.NewSynthesisRepo(synthesisSchema.WithContext(s.ctx)).Update(m, search.NewQueryBuilder().
		Eq(synthesisSchema.ID, req.ID).Eq(synthesisSchema.Status, enums.SynthesisStatusDown.Val()).Build())
	if err != nil {
		return nil, err
	}
	// 日志
	_ = logic.NewSynthesisLog(s.ctx, enums.LogActionDelete, req.ID).Save(s.GetAdminId())
	return &define.DelSynthesisResp{}, nil
}

// GetSynthesisLogList 查询合成操作日志列表
func (s *Service) GetSynthesisLogList(req *define.GetSynthesisLogListReq) ([]*define.GetSynthesisLogListResp, error) {
	logSchema := repo.GetQuery().SynthesisLog
	builder := search.NewQueryBuilder().Eq(logSchema.SynthesisID, req.SynthesisID).OrderByDesc(logSchema.ID)
	listData, err := repo.NewSynthesisLogRepo(logSchema.WithContext(s.ctx)).SelectList(builder.Build())
	if err != nil {
		return nil, err
	}
	resp := make([]*define.GetSynthesisLogListResp, 0)
	for _, v := range listData {
		resp = append(resp, &define.GetSynthesisLogListResp{
			Action:    v.Action,
			Content:   logic.GetLogContent(s.ctx, v),
			CreatedAt: v.CreatedAt,
			CreatedBy: v.CreatedBy,
		})
	}
	return resp, nil
}

// GetSynthesisRuleAdmin 查询合成规则
func (s *Service) GetSynthesisRuleAdmin() (*define.GetSynthesisRuleAdminResp, error) {
	resp := &define.GetSynthesisRuleAdminResp{}
	err := facade.GetObj(s.ctx, enums.SynthesisRuleConfigKey, &resp.Content)
	if err != nil {
		log.Ctx(s.ctx).Errorf("GetSynthesisRuleAdmin err:%v", err)
		return nil, err
	}
	return resp, nil
}

// EditSynthesisRule 编辑合成规则
func (s *Service) EditSynthesisRule(req *define.EditSynthesisRuleReq) (*define.EditSynthesisRuleResp, error) {
	err := facade.SaveOrUpdate(s.ctx, enums.SynthesisRuleConfigKey, req.Content)
	if err != nil {
		log.Ctx(s.ctx).Errorf("EditSynthesisRule err:%v", err)
		return nil, err
	}
	return nil, nil
}

// GetSynthesisAdminOrderList 合成订单列表
func (s *Service) GetSynthesisAdminOrderList(req *define.GetSynthesisAdminOrderListReq) (*define.GetSynthesisAdminOrderListResp, error) {
	synthesisOrderSchema := repo.GetQuery().SynthesisOrder
	builder := search.NewQueryBuilder().OrderByDesc(synthesisOrderSchema.ID)
	if !req.CreatedAtStart.IsZero() && !req.CreatedAtEnd.IsZero() {
		builder = builder.Gte(synthesisOrderSchema.CreatedAt, req.CreatedAtStart)
		builder = builder.Lte(synthesisOrderSchema.CreatedAt, req.CreatedAtEnd)
	}
	if req.ActivityID > 0 {
		builder = builder.Eq(synthesisOrderSchema.SynthesisID, req.ActivityID)
	}
	if req.ActivityTitle != "" {
		builder = builder.Like(synthesisOrderSchema.SynthesisTitle, "%"+req.ActivityTitle+"%")
	}
	if req.ItemId != "" {
		builder = builder.Eq(synthesisOrderSchema.ItemID, req.ItemId)
	}
	if req.ItemTitle != "" {
		builder = builder.Like(synthesisOrderSchema.ItemTitle, "%"+req.ItemTitle+"%")
	}
	if req.OrderID != "" {
		builder = builder.Eq(synthesisOrderSchema.OrderID, req.OrderID)
	}
	if req.UserID != "" {
		builder = builder.Eq(synthesisOrderSchema.UserID, req.UserID)
	}
	if req.Status != 0 {
		builder = builder.Eq(synthesisOrderSchema.Status, req.Status)
	}
	if req.ChainStatus != nil && *req.ChainStatus >= 0 {
		builder = builder.Eq(synthesisOrderSchema.ChainStatus, *req.ChainStatus)
	}
	if req.ActivityType != nil && *req.ActivityType >= 0 {
		builder = builder.Eq(synthesisOrderSchema.ActivityType, *req.ChainStatus)
	}
	list, count, err := repo.NewSynthesisOrderRepo(synthesisOrderSchema.WithContext(s.ctx)).SelectPage(builder.Build(), req.GetPage(), req.GetPageSize())
	if err != nil {
		log.Ctx(s.ctx).Errorf("GetSynthesisAdminOrderList err:%v", err)
		return nil, err
	}
	resp := &define.GetSynthesisAdminOrderListResp{
		List:  make([]*define.GetSynthesisAdminOrderListData, 0),
		Total: count,
	}
	if len(list) == 0 {
		return resp, nil
	}
	synthesisIDs := make([]int64, 0)
	for _, item := range list {
		synthesisIDs = append(synthesisIDs, item.SynthesisID)
	}
	activityList, err := logic.GetSynthesisMap(s.ctx, synthesisIDs)
	if err != nil {
		log.Ctx(s.ctx).Errorf("GetSynthesisAdminOrderList err:%v", err)
		return nil, err
	}
	for _, item := range list {
		aType := int32(0)
		itemImageURL := ""
		if activityList[item.SynthesisID] != nil {
			aType = activityList[item.SynthesisID].ActivityType
			itemImageURL = activityList[item.SynthesisID].ItemImageURL
		}
		data := &define.GetSynthesisAdminOrderListData{
			ID:            item.ID,
			OrderID:       item.OrderID,
			ActivityID:    item.SynthesisID,
			ActivityTitle: item.SynthesisTitle,
			ActivityType:  aType,
			ItemTitle:     item.ItemTitle,
			Qty:           item.Qty,
			UserID:        item.UserID,
			Status:        item.Status,
			ChainStatus:   item.ChainStatus,
			CreatedAt:     item.CreatedAt,
		}
		if item.ActivityType == enums.Rights.Val() {
			data.ItemCoverURL = item.ItemCoverURL
		} else {
			data.ItemCoverURL = itemImageURL
		}
		resp.List = append(resp.List, data)
	}
	return resp, nil
}

// GetSynthesisAdminOrderDetail 合成订单详情
func (s *Service) GetSynthesisAdminOrderDetail(req *define.GetSynthesisAdminOrderDetailReq) (*define.GetSynthesisAdminOrderDetailResp, error) {
	synthesisOrderSchema := repo.GetQuery().SynthesisOrder
	builder := search.NewQueryBuilder().Eq(synthesisOrderSchema.ID, req.ID)
	m, err := repo.NewSynthesisOrderRepo(synthesisOrderSchema.WithContext(s.ctx)).SelectOne(builder.Build())
	if err != nil {
		log.Ctx(s.ctx).Errorf("GetSynthesisAdminOrderDetail err:%v", err)
		return nil, err
	}
	err = repo.GetDB().Model(m).Association("SynthesisOrderDetail").Find(&m.SynthesisOrderDetail)
	if err != nil {
		log.Ctx(s.ctx).Errorf("GetSynthesisAdminOrderDetail err:%v", err)
		return nil, err
	}
	if err = repo.GetDB().Model(m).Association("Synthesis").Find(&m.Synthesis); err != nil {
		log.Ctx(s.ctx).Errorf("GetSynthesisAdminOrderDetail err:%v", err)
		return nil, err
	}
	itemDetail := &define.GetSynthesisAdminOrderItemDetail{
		ID:        m.ItemID,
		ItemTitle: m.ItemTitle,
		CoverURL:  m.ItemCoverURL,
	}
	if err = json.Unmarshal(m.ItemInfo, &itemDetail); err != nil {
		log.Ctx(s.ctx).Errorf("GetSynthesisAdminOrderDetail err:%v", err)
	}
	userNickname := ""
	userInfo, err := userFacade.GetNodeUser(s.ctx, m.UserID)
	if err != nil {
		log.Ctx(s.ctx).Errorf("GetSynthesisAdminOrderDetail GetNodeUser err:%v", err)
	}
	if userInfo != nil {
		userNickname = userInfo.PatbgDetail.Nickname
	}
	resp := &define.GetSynthesisAdminOrderDetailResp{
		ID:            m.ID,
		OrderID:       m.OrderID,
		ActivityID:    m.SynthesisID,
		ActivityTitle: m.SynthesisTitle,
		ActivityType:  m.ActivityType,
		Qty:           m.Qty,
		UserID:        m.UserID,
		UserNickname:  userNickname,
		Status:        m.Status,
		ChainHash:     m.ChainHash,
		CreatedAt:     m.CreatedAt,
		ItemDetail:    itemDetail,
	}
	return resp, nil
}

// ExportSynthesisAdminOrderList 导出合成订单
func (s *Service) ExportSynthesisAdminOrderList(req *define.GetSynthesisAdminOrderListReq) error {
	dataList := make([]*define.GetSynthesisAdminOrderListData, 0)
	for i := 1; i < 10000; i++ {
		req.PageSize = 100
		req.Page = i
		page, err := s.GetSynthesisAdminOrderList(req)
		if err != nil {
			return err
		}
		if len(page.List) == 0 {
			break
		}
		dataList = append(dataList, page.List...)
	}
	excel := excelize_lib.NewExcel()
	dataKey := make([]map[string]string, 0)
	dataKey = append(dataKey, map[string]string{"key": "created_at", "title": "融合时间", "width": "30", "is_num": "0"})
	dataKey = append(dataKey, map[string]string{"key": "order_id", "title": "订单号", "width": "30", "is_num": "0"})
	dataKey = append(dataKey, map[string]string{"key": "activity_title", "title": "活动名称", "width": "20", "is_num": "0"})
	dataKey = append(dataKey, map[string]string{"key": "activity_id", "title": "活动ID", "width": "30", "is_num": "0"})
	dataKey = append(dataKey, map[string]string{"key": "item_title", "title": "融合物品", "width": "20", "is_num": "0"})
	dataKey = append(dataKey, map[string]string{"key": "item_cover_url", "title": "商品图", "width": "20", "is_num": "0"})
	dataKey = append(dataKey, map[string]string{"key": "activity_type", "title": "融合类型", "width": "20", "is_num": "0"})
	dataKey = append(dataKey, map[string]string{"key": "qty", "title": "融合数量", "width": "30", "is_num": "0"})
	dataKey = append(dataKey, map[string]string{"key": "user_id", "title": "用户ID", "width": "20", "is_num": "0"})
	dataKey = append(dataKey, map[string]string{"key": "status", "title": "订单状态", "width": "20", "is_num": "0"})
	dataKey = append(dataKey, map[string]string{"key": "chain_status", "title": "上链状态", "width": "20", "is_num": "0"})

	data := make([]map[string]interface{}, 0)
	for _, idata := range dataList {

		data = append(data, map[string]interface{}{
			"created_at":     util.GetDateTimeFormatStr(idata.CreatedAt),
			"order_id":       idata.OrderID,
			"activity_title": idata.ActivityTitle,
			"activity_id":    idata.ActivityID,
			"item_title":     idata.ItemTitle,
			"item_cover_url": idata.ItemCoverURL,
			"activity_type":  enums.SynthesisActivityTypeMap[idata.ActivityType],
			"qty":            idata.Qty,
			"user_id":        idata.UserID,
			"status":         enums.SynthesisOrderStatusMap[idata.Status],
			"chain_status":   enums.SynthesisOrderChainStatusMap[idata.ChainStatus],
		})
	}

	err := excel.ExportToStream(dataKey, data, s.ctx.(*gin.Context))
	if err != nil {
		log.Ctx(s.ctx).Errorf("[Service.ExportSynthesisAdminOrderList] ExportToWeb err:%v", err)
		return response.SystemErr
	}
	return nil
}

// GetPriorityBuyList 查询优先购列表
func (s *Service) GetPriorityBuyList(req *define.GetPriorityBuyListReq) (*define.GetPriorityBuyListResp, error) {
	buysReq := &issueDefine.GetPriorityBuysReq{}
	_ = copier.Copy(buysReq, req)
	priorityBuy, count, err := issueFacade.GetPriorityBuys(s.ctx, buysReq)
	if err != nil {
		log.Ctx(s.ctx).Errorf("[Service.GetPriorityBuyList] GetPriorityBuys err:%v", err)
		return nil, err
	}
	log.Ctx(s.ctx).Infof("[Service.GetPriorityBuyList] GetPriorityBuys req:%+v, resp:%+v, count:%+v", buysReq, priorityBuy, count)
	list := make([]*define.GetPriorityBuyListData, 0)
	itemIds := make([]string, 0)
	for _, item := range priorityBuy {
		if item.ItemID == nil || item.ItemID.IsZero() {
			continue
		}
		itemIds = append(itemIds, item.ItemID.Hex())
	}
	itemsMap, err := issueFacade.GetIssueItemMap(s.ctx, itemIds)
	if err != nil {
		log.Ctx(s.ctx).Errorf("[Service.GetPriorityBuyList] GetIssueItemMap err:%v", err)
		return nil, err
	}
	for _, item := range priorityBuy {
		data := &define.GetPriorityBuyListData{
			ID:   item.ID.Hex(),
			Name: item.Name,
		}
		if item.IssueItemID != nil && !item.IssueItemID.IsZero() {
			data.IssueItemID = item.IssueItemID.Hex()
		}
		if item.Creator != nil && !item.Creator.IsZero() {
			data.Creator = item.Creator.Hex()
		}
		if item.ItemID != nil && !item.ItemID.IsZero() {
			data.ItemID = item.ItemID.Hex()
			if itemInfo, ok := itemsMap[item.ItemID.Hex()]; ok {
				data.ItemName = itemInfo.ItemName
				data.ImageURL = itemInfo.ImageURL
				data.Price = itemInfo.Price
				data.PriorityBuy = &define.GetPriorityBuyItem{
					Status:         int32(itemInfo.Status),
					Stock:          itemInfo.PriorityBuy.Stock,
					AdvanceMinutes: itemInfo.PriorityBuy.AdvanceMinutes,
				}
				usedStock, err := logic.GetPriorityBuyUsableStock(s.ctx, item.ID.Hex())
				if err != nil {
					return nil, err
				}
				data.PriorityBuy.UsedStock = usedStock
			}
		}
		list = append(list, data)
	}
	resp := &define.GetPriorityBuyListResp{
		List:  list,
		Total: count,
	}
	return resp, nil
}

// GetItemSynthesisList 查询融合商品奖品列表
func (s *Service) GetItemSynthesisList(req *define.GetItemSynthesisListReq) (*define.GetItemSynthesisListResp, error) {
	itemReq := &issueDefine.GetItemSynthesisListReq{}
	_ = copier.Copy(itemReq, req)
	itemList, count, err := issueFacade.GetIssueItemsBySynthesis(s.ctx, itemReq)
	if err != nil {
		log.Ctx(s.ctx).Errorf("[Service.GetItemSynthesisList] GetIssueItemsBySynthesis err:%v", err)
		return nil, err
	}
	log.Ctx(s.ctx).Infof("[Service.GetItemSynthesisList] GetIssueItemsBySynthesis req:%+v, resp:%+v, count:%+v", itemReq, itemList, count)
	list := make([]*define.GetItemSynthesisListData, 0)
	for _, item := range itemList {
		data := &define.GetItemSynthesisListData{
			ItemName: item.ItemName,
			ImageURL: item.ImageURL,
			Price:    item.Price,
			Creator:  item.AdminID.Hex(),
		}
		if !item.ItemID.IsZero() {
			data.ItemID = item.ItemID.Hex()
		}
		usedStock, _ := logic.GetItemUsableStock(s.ctx, item.ItemID.Hex())
		data.UsableStock = usedStock
		list = append(list, data)
	}
	resp := &define.GetItemSynthesisListResp{
		List:  list,
		Total: count,
	}
	return resp, nil
}

// GetSynthesisOrderDetailList 获取合成订单详情列表
func (s *Service) GetSynthesisOrderDetailList(req *define.GetSynthesisOrderDetailListReq) (*define.GetSynthesisOrderDetailListResp, error) {
	synthesisOrderDetailSchema := repo.GetQuery().SynthesisOrderDetail
	builder := search.NewQueryBuilder().Eq(synthesisOrderDetailSchema.SynthesisOrderID, req.ID).OrderByDesc(synthesisOrderDetailSchema.ID)
	list, count, err := repo.NewSynthesisOrderDetailRepo(synthesisOrderDetailSchema.WithContext(s.ctx)).
		SelectPage(builder.Build(), req.GetPage(), req.GetPageSize())
	if err != nil {
		log.Ctx(s.ctx).Errorf("GetSynthesisAdminOrderList err:%v", err)
		return nil, err
	}
	resp := &define.GetSynthesisOrderDetailListResp{
		List:  make([]*define.GetSynthesisOrderDetailListData, 0),
		Total: count,
	}
	if len(list) == 0 {
		return resp, nil
	}
	var userItemIds []string
	for _, item := range list {
		userItemIds = append(userItemIds, item.UserItemID)
	}
	costMap := make(map[string]int64)
	if len(userItemIds) > 0 {
		// 成本价
		costResList, err := yc_open.QueryUserItemsCostPrice(s.ctx, userItemIds)
		if err != nil {
			log.Ctx(s.ctx).Errorf("GetSynthesisOrderDetailList QueryUserItemsCostPrice err:%v", err)
		}
		for _, item := range costResList {
			costMap[item.ID] = item.Cost
		}
	}
	materialsDetail := make([]*define.GetSynthesisOrderDetailListData, 0)
	for _, item := range list {
		detail := &define.GetSynthesisOrderDetailListData{
			ID:       item.MaterialsItemID,
			ItemName: item.MaterialsItemName,
			CoverURL: item.MaterialsItemURL,
		}
		if item.MaterialsItemInfo != nil {
			if err = json.Unmarshal(*item.MaterialsItemInfo, &detail); err != nil {
				log.Ctx(s.ctx).Errorf("GetSynthesisOrderDetailList Unmarshal err:%v", err)
			}
		}
		detail.Code = item.UserItemID
		detail.UserItemID = item.UserItemID
		detail.CostPrice = costMap[item.UserItemID]
		materialsDetail = append(materialsDetail, detail)
	}
	resp.List = materialsDetail
	return resp, nil
}
